#!/usr/bin/env python3
"""
Layout Bricks Instructions Analyzer

This script analyzes all .txt files in a folder and its subfolders to detect
"layout bricks instructions" using LiteLLM with OpenRouter's DeepSeek model.
Now supports parallel processing with rate limiting.

Requirements:
- litellm
- openrouter API key
- asyncio (built-in)

Usage:
    python analyze_layout_bricks.py <input_folder> [output_folder]
"""

import os
import json
import csv
import logging
import argparse
import asyncio
import time
import litellm
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Set

from pydantic import BaseModel

# Try to load .env file if python-dotenv is available
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass  # python-dotenv not installed, continue without it

# Configure logging
logging.basicConfig(
    level=logging.WARNING,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('   analysis.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AnalysisResult(BaseModel):
    """Pydantic model for structured AI response."""
    score: int
    reasoning: str
    keywords_found: List[str]
    confidence: str

class RateLimiter:
    """Rate limiter to ensure we don't exceed API rate limits."""

    def __init__(self, max_calls_per_minute: int = 18):
        self.max_calls_per_minute = max_calls_per_minute
        self.calls = []
        self.lock = asyncio.Lock()

    async def acquire(self):
        """Acquire permission to make an API call."""
        async with self.lock:
            now = time.time()
            # Remove calls older than 1 minute
            self.calls = [call_time for call_time in self.calls if now - call_time < 60]

            # If we're at the limit, wait until we can make another call
            if len(self.calls) >= self.max_calls_per_minute:
                # Wait until the oldest call is more than 1 minute old
                oldest_call = min(self.calls)
                wait_time = 60 - (now - oldest_call) + 0.1  # Add small buffer
                if wait_time > 0:
                    logger.info(f"Rate limit reached. Waiting {wait_time:.1f} seconds...")
                    await asyncio.sleep(wait_time)
                    # Refresh the calls list after waiting
                    now = time.time()
                    self.calls = [call_time for call_time in self.calls if now - call_time < 60]

            # Record this call
            self.calls.append(now)

class LayoutBricksAnalyzer:
    def __init__(self, api_key: str = None, prompt_file: str = "_prompt.txt", max_workers: int = 4, max_calls_per_minute: int = 18):
        """Initialize the analyzer with OpenRouter API key and prompt file."""
        self.api_key = api_key or os.getenv('OPENROUTER_API_KEY')
        if not self.api_key:
            raise ValueError("OpenRouter API key is required. Set OPENROUTER_API_KEY environment variable or pass it directly.")

        # Configure LiteLLM for OpenRouter
        self.model = "openrouter/deepseek/deepseek-r1-0528:free"
        self.prompt_file = prompt_file
        self.prompt_template = self.load_prompt_template()
        self.processed_files: Set[str] = set()
        self.max_workers = max_workers
        self.rate_limiter = RateLimiter(max_calls_per_minute)

    def load_prompt_template(self) -> str:
        """Load the prompt template from file."""
        try:
            with open(self.prompt_file, 'r', encoding='utf-8') as f:
                template = f.read()
            logger.info(f"Loaded prompt template from: {self.prompt_file}")
            return template
        except FileNotFoundError:
            logger.error(f"Prompt file not found: {self.prompt_file}")
            raise FileNotFoundError(f"Prompt template file '{self.prompt_file}' not found. Please create this file with your analysis prompt.")
        except Exception as e:
            logger.error(f"Error loading prompt template: {e}")
            raise

    def create_analysis_prompt(self, file_content: str) -> str:
        """Create the prompt for analyzing file content using the loaded template."""
        return self.prompt_template.format(file_content=file_content)

    async def analyze_file(self, file_path: Path) -> Dict:
        """Analyze a single file for layout bricks instructions."""
        try:
            # Read file content
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            if not content.strip():
                return {
                    "file_path": str(file_path),
                    "score": 1,
                    "reasoning": "File is empty",
                    "keywords_found": [],
                    "confidence": "high",
                    "error": None
                }

            # Create prompt
            prompt = self.create_analysis_prompt(content)

            # Wait for rate limiter permission
            await self.rate_limiter.acquire()

            # Call LiteLLM with OpenRouter using structured output
            response = await asyncio.to_thread(
                litellm.completion,
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a helpful assistant designed to output JSON for analyzing text content."},
                    {"role": "user", "content": prompt}
                ],
                response_format=AnalysisResult,
                temperature=0.1,
                max_tokens=1000,
                api_key=self.api_key
            )

            # Parse structured response
            response_text = response.choices[0].message.content
            logger.debug(f"Structured response for {file_path}: {response_text}")

            # Parse the JSON response (should be valid JSON now)
            try:
                analysis_result = json.loads(response_text)

            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse structured JSON response for {file_path}: {e}")
                # Fallback: create basic result
                analysis_result = {
                    "score": 1,
                    "reasoning": f"Failed to parse structured response: {str(e)}. Raw response: {response_text[:200]}...",
                    "keywords_found": [],
                    "confidence": "low"
                }

            # Add file path and ensure score is valid
            analysis_result["file_path"] = str(file_path)
            analysis_result["score"] = max(1, min(10, int(analysis_result.get("score", 1))))
            analysis_result["error"] = None

            return analysis_result

        except Exception as e:
            logger.error(f"Error analyzing file {file_path}: {e}")
            return {
                "file_path": str(file_path),
                "score": 1,
                "reasoning": f"Error during analysis: {str(e)}",
                "keywords_found": [],
                "confidence": "low",
                "error": str(e)
            }

    def find_txt_files(self, input_folder: Path) -> List[Path]:
        """Find all .txt files in the folder and subfolders."""
        txt_files = []
        for root, dirs, files in os.walk(input_folder):
            for file in files:
                if file.lower().endswith('.txt'):
                    txt_files.append(Path(root) / file)
        return txt_files

    def save_reasoning_file(self, analysis_result: Dict, output_folder: Path):
        """Save detailed reasoning to a separate file."""
        file_path = Path(analysis_result["file_path"])

        # Create a safe filename based on the original file path
        safe_filename = str(file_path).replace('\\', '_').replace('/', '_').replace(':', '_')
        reasoning_filename = f"{safe_filename}_analysis.txt"
        reasoning_path = output_folder / reasoning_filename

        # Ensure output directory exists
        reasoning_path.parent.mkdir(parents=True, exist_ok=True)

        # Write reasoning file
        with open(reasoning_path, 'w', encoding='utf-8') as f:
            f.write(f"Layout Bricks Instructions Analysis\n")
            f.write(f"{'=' * 50}\n\n")
            f.write(f"Original File: {analysis_result['file_path']}\n")
            f.write(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Score: {analysis_result['score']}/10\n")
            f.write(f"Confidence: {analysis_result['confidence']}\n\n")
            f.write(f"Keywords Found: {', '.join(analysis_result['keywords_found'])}\n\n")
            f.write(f"Detailed Reasoning:\n")
            f.write(f"{'-' * 20}\n")
            f.write(f"{analysis_result['reasoning']}\n")

            if analysis_result.get('error'):
                f.write(f"\nError: {analysis_result['error']}\n")

    def load_processed_files(self, output_path: Path):
        """Load list of already processed files from CSV if it exists."""
        csv_path = output_path / "analysis_results.csv"
        if csv_path.exists():
            try:
                with open(csv_path, 'r', encoding='utf-8', newline='') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        self.processed_files.add(row['file_path'])
                logger.info(f"Loaded {len(self.processed_files)} already processed files")
            except Exception as e:
                logger.warning(f"Could not load processed files list: {e}")

    def save_to_csv(self, results: List[Dict], output_path: Path):
        """Save results to CSV file."""
        csv_path = output_path / "analysis_results.csv"

        # Define CSV headers
        headers = ['file_path', 'score', 'confidence', 'keywords_found', 'reasoning', 'error', 'analysis_date']

        # Check if file exists to determine if we need to write headers
        file_exists = csv_path.exists()

        with open(csv_path, 'a', encoding='utf-8', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=headers)

            # Write headers only if file doesn't exist
            if not file_exists:
                writer.writeheader()

            # Write results
            for result in results:
                # Convert keywords list to string
                keywords_str = ', '.join(result.get('keywords_found', []))

                writer.writerow({
                    'file_path': result['file_path'],
                    'score': result['score'],
                    'confidence': result['confidence'],
                    'keywords_found': keywords_str,
                    'reasoning': result['reasoning'],
                    'error': result.get('error', ''),
                    'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })

        logger.info(f"Results saved to CSV: {csv_path}")

    async def process_file_worker(self, file_path: Path, output_path: Path, semaphore: asyncio.Semaphore, csv_lock: asyncio.Lock) -> Dict:
        """Worker function to process a single file with concurrency control."""
        async with semaphore:
            try:
                result = await self.analyze_file(file_path)

                # Save reasoning file
                self.save_reasoning_file(result, output_path)

                # Save to CSV immediately (with lock to prevent concurrent writes)
                async with csv_lock:
                    self.save_to_csv([result], output_path)

                # Log result
                logger.info(f"File: {result['file_path']} | Score: {result['score']}/10 | Saved to CSV")

                return result

            except Exception as e:
                logger.error(f"Failed to analyze {file_path}: {e}")
                error_result = {
                    "file_path": str(file_path),
                    "score": 1,
                    "reasoning": f"Analysis failed: {str(e)}",
                    "keywords_found": [],
                    "confidence": "low",
                    "error": str(e)
                }

                # Save error result to CSV immediately
                async with csv_lock:
                    self.save_to_csv([error_result], output_path)

                return error_result

    async def analyze_folder_async(self, input_folder: str, output_folder: str = None) -> List[Dict]:
        """Async version of analyze_folder with parallel processing."""
        input_path = Path(input_folder)

        if not input_path.exists():
            raise FileNotFoundError(f"Input folder does not exist: {input_folder}")

        # Set up output folder
        if output_folder:
            output_path = Path(output_folder)
        else:
            output_path = Path("analysis_output")

        output_path.mkdir(parents=True, exist_ok=True)

        # Load already processed files
        self.load_processed_files(output_path)

        # Find all txt files
        txt_files = self.find_txt_files(input_path)
        logger.info(f"Found {len(txt_files)} .txt files to analyze")

        # Filter out already processed files
        unprocessed_files = [f for f in txt_files if str(f) not in self.processed_files]

        if len(unprocessed_files) < len(txt_files):
            skipped_count = len(txt_files) - len(unprocessed_files)
            logger.info(f"Skipping {skipped_count} already processed files")

        if not unprocessed_files:
            if not txt_files:
                logger.warning("No .txt files found in the specified folder")
            else:
                logger.info("All files have already been processed")
            return []

        logger.info(f"Processing {len(unprocessed_files)} new files with {self.max_workers} workers")

        # Create semaphore to limit concurrent workers and lock for CSV writing
        semaphore = asyncio.Semaphore(self.max_workers)
        csv_lock = asyncio.Lock()

        # Create tasks for all files
        tasks = [
            self.process_file_worker(file_path, output_path, semaphore, csv_lock)
            for file_path in unprocessed_files
        ]

        # Process all files concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Filter out exceptions and convert them to error results
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Task failed for {unprocessed_files[i]}: {result}")
                processed_results.append({
                    "file_path": str(unprocessed_files[i]),
                    "score": 1,
                    "reasoning": f"Task failed: {str(result)}",
                    "keywords_found": [],
                    "confidence": "low",
                    "error": str(result)
                })
            else:
                processed_results.append(result)

        # Save results if we have any new ones
        if processed_results:
            # Save summary results (JSON) - CSV is already saved per file
            summary_path = output_path / "analysis_summary.json"

            # Load existing results if file exists
            all_results = []
            if summary_path.exists():
                try:
                    with open(summary_path, 'r', encoding='utf-8') as f:
                        all_results = json.load(f)
                except Exception as e:
                    logger.warning(f"Could not load existing summary: {e}")

            # Add new results
            all_results.extend(processed_results)

            # Save updated summary
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(all_results, f, indent=2, ensure_ascii=False)

        logger.info(f"Analysis complete. Results saved to: {output_path}")
        return processed_results

    def analyze_folder(self, input_folder: str, output_folder: str = None) -> List[Dict]:
        """Analyze all txt files in the specified folder (synchronous wrapper for async method)."""
        return asyncio.run(self.analyze_folder_async(input_folder, output_folder))

def main():
    parser = argparse.ArgumentParser(description="Analyze text files for layout bricks instructions")
    parser.add_argument("--input_folder", default ="input_ride", help="Path to the folder containing .txt files to analyze")
    parser.add_argument("--output-folder",default="output_ride", help="Path to the output folder (optional)")
    parser.add_argument("--api-key", help="OpenRouter API key (optional, can use OPENROUTER_API_KEY env var)")
    parser.add_argument("--prompt-file", default="_prompt.txt", help="Path to the prompt template file (default: _prompt.txt)")
    parser.add_argument("--max-workers", type=int, default=4, help="Maximum number of parallel workers (default: 4)")
    parser.add_argument("--max-calls-per-minute", type=int, default=18, help="Maximum API calls per minute (default: 18)")

    args = parser.parse_args()

    try:
        # Initialize analyzer
        analyzer = LayoutBricksAnalyzer(
            api_key=args.api_key,
            prompt_file=args.prompt_file,
            max_workers=args.max_workers,
            max_calls_per_minute=args.max_calls_per_minute
        )

        # Run analysis
        results = analyzer.analyze_folder(args.input_folder, args.output_folder)

        # Print summary
        print(f"\n{'='*60}")
        print("ANALYSIS SUMMARY")
        print(f"{'='*60}")

        if not results:
            print("No new files were processed in this run.")
            print("All files may have been processed already.")
        else:
            for result in results:
                print(f"File: {result['file_path']}")
                print(f"Score: {result['score']}/10")
                print(f"Confidence: {result['confidence']}")
                if result.get('error'):
                    print(f"Error: {result['error']}")
                print("-" * 40)

            # Statistics
            scores = [r['score'] for r in results if not r.get('error')]
            if scores:
                avg_score = sum(scores) / len(scores)
                high_scores = len([s for s in scores if s >= 7])
                print(f"\nStatistics for this run:")
                print(f"New files analyzed: {len(results)}")
                print(f"Average score: {avg_score:.2f}")
                print(f"Files with high scores (7+): {high_scores}")

        print(f"\nResults saved to CSV and JSON in the output folder.")
        print(f"CSV file: analysis_results.csv")
        print(f"JSON file: analysis_summary.json")

    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())
